/* Form Reports Component Styles */

.form-reports-row {
  display: flex;
  flex-wrap: wrap;
  align-items: end;
  gap: 10px;
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.form-control {
  border: 1px solid #d2d9e5;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease-in-out;
}

.form-control:focus {
  border-color: #0190fe;
  box-shadow: 0 0 0 0.2rem rgba(1, 144, 254, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #dc3545;
}

/* Autocomplete Dropdown Styles */
.autocomplete-container {
  position: relative;
}

.autocomplete-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d2d9e5;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  list-style: none;
  padding: 0;
  margin: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.autocomplete-option {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  font-size: 0.9rem;
}

.autocomplete-option:hover {
  background-color: #f8f9fa;
  color: #0190fe;
}

.autocomplete-option:last-child {
  border-bottom: none;
}

/* Toggle Switch Styles */
.form-check {
  display: flex;
  align-items: center;
  min-height: 1.5rem;
}

.form-check-input {
  width: 2rem;
  height: 1rem;
  margin-right: 0.5rem;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.form-check-input:checked {
  background-color: #0190fe;
  border-color: #0190fe;
}

.form-check-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
}

/* Action Buttons */
.form-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  font-size: 0.9rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background-color: #0190fe;
  color: white;
}

.btn-primary:hover {
  background-color: #0170d4;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #218838;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-reports-row {
    gap: 8px;
  }
  
  .col-lg-1 {
    min-width: 140px;
  }
  
  .col-lg-2 {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .form-reports-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .form-reports-row > div {
    width: 100% !important;
    max-width: none !important;
  }
  
  .form-actions {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn {
    justify-content: center;
  }
}

/* Card Styling */
.card {
  border: 1px solid #e4e9f0;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e9f0;
  padding: 1rem 1.25rem;
}

.card-block {
  padding: 1.5rem;
}

.cat__core__title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

/* Utility Classes */
.text-danger {
  color: #dc3545 !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.d-flex {
  display: flex !important;
}

.align-items-end {
  align-items: flex-end !important;
}
