import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { StructureService } from '../structure.service';

@Component({
  selector: 'app-form-reports',
  templateUrl: './form-reports.component.html',
  styleUrls: ['./form-reports.component.css']
})
export class FormReportsComponent implements OnInit {
  formReportsForm: FormGroup;
  isSubmitted = false;
  showDownloadButton = false;

  // Static data for dropdowns
  formStatusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' }
  ];

  specificFormOptions = [
    'Intake Form',
    'Consent Form', 
    'Discharge Form'
  ];

  siteOptions = [
    { id: 1, itemName: 'Atlanta-test' },
    { id: 2, itemName: 'Austin TX' },
    { id: 3, itemName: 'New York' },
    { id: 4, itemName: 'LA Branch' }
  ];

  // Site dropdown settings (matching existing style)
  selectedSiteItem = [];
  siteDropdownSettings = {
    singleSelection: true,
    text: 'Select Site',
    classes: 'myclass custom-class',
    enableSearchFilter: true,
    enableFilterSelectAll: false
  };

  // Autocomplete for Specific Form
  filteredFormOptions: string[] = [];
  showFormDropdown = false;

  constructor(
    private formBuilder: FormBuilder,
    private structureService: StructureService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.filteredFormOptions = [...this.specificFormOptions];
  }

  initializeForm(): void {
    this.formReportsForm = this.formBuilder.group({
      formStatus: ['', Validators.required],
      specificForm: [''],
      site: [''],
      mrn: [''],
      firstNameStartsWith: [''],
      lastNameStartsWith: [''],
      clinicianContains: [''],
      drugNameContains: [''],
      missingRecords: [false]
    });
  }

  // Site dropdown methods
  onSiteSelect(item: any): void {
    this.formReportsForm.patchValue({ site: item.id });
  }

  onSiteDeSelect(): void {
    this.formReportsForm.patchValue({ site: '' });
    this.selectedSiteItem = [];
  }

  // Autocomplete methods for Specific Form
  onFormInputChange(event: any): void {
    const value = event.target.value.toLowerCase();
    this.filteredFormOptions = this.specificFormOptions.filter(option =>
      option.toLowerCase().includes(value)
    );
    this.showFormDropdown = this.filteredFormOptions.length > 0 && value.length > 0;
  }

  selectFormOption(option: string): void {
    this.formReportsForm.patchValue({ specificForm: option });
    this.showFormDropdown = false;
  }

  hideFormDropdown(): void {
    setTimeout(() => {
      this.showFormDropdown = false;
    }, 200);
  }

  onSubmit(): void {
    this.isSubmitted = true;
    
    if (this.formReportsForm.valid) {
      const formData = {
        ...this.formReportsForm.value,
        selectedSite: this.selectedSiteItem.length > 0 ? this.selectedSiteItem[0] : null
      };
      
      console.log('Form Reports Data:', formData);
      this.showDownloadButton = true;
    } else {
      console.log('Form is invalid');
    }
  }

  downloadReport(): void {
    // Simulate file download
    const dummyData = 'Form Reports Data\nGenerated on: ' + new Date().toISOString();
    const blob = new Blob([dummyData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'form-reports.csv';
    link.click();
    window.URL.revokeObjectURL(url);
  }

  resetForm(): void {
    this.formReportsForm.reset();
    this.selectedSiteItem = [];
    this.isSubmitted = false;
    this.showDownloadButton = false;
    this.filteredFormOptions = [...this.specificFormOptions];
  }
}
