<section class="card">
  <div class="card-header">
    <span class="cat__core__title">
      <strong>Form Reports</strong>
    </span>
  </div>
  <div class="card-block">
    <form [formGroup]="formReportsForm" (ngSubmit)="onSubmit()" class="form-horizontal">
      
      <!-- Single Row Form Layout -->
      <div class="row form-reports-row">
        
        <!-- Form Status Dropdown -->
        <div class="col-lg-2 col-md-3 col-sm-6 col-12 mb-3">
          <label for="formStatus" class="form-label">Form Status <span class="text-danger">*</span></label>
          <select 
            id="formStatus" 
            formControlName="formStatus" 
            class="form-control"
            [class.is-invalid]="isSubmitted && formReportsForm.get('formStatus')?.invalid">
            <option value="">Select Status</option>
            <option *ngFor="let status of formStatusOptions" [value]="status.value">
              {{ status.label }}
            </option>
          </select>
          <div *ngIf="isSubmitted && formReportsForm.get('formStatus')?.invalid" class="invalid-feedback">
            Form Status is required
          </div>
        </div>

        <!-- Specific Form Autocomplete -->
        <div class="col-lg-2 col-md-3 col-sm-6 col-12 mb-3">
          <label for="specificForm" class="form-label">Specific Form</label>
          <div class="autocomplete-container">
            <input 
              type="text" 
              id="specificForm"
              formControlName="specificForm"
              class="form-control"
              placeholder="Type to search..."
              (input)="onFormInputChange($event)"
              (blur)="hideFormDropdown()"
              autocomplete="off">
            <ul class="autocomplete-dropdown" *ngIf="showFormDropdown">
              <li *ngFor="let option of filteredFormOptions" 
                  (mousedown)="selectFormOption(option)"
                  class="autocomplete-option">
                {{ option }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Site Dropdown -->
        <div class="col-lg-2 col-md-3 col-sm-6 col-12 mb-3">
          <label for="site" class="form-label">Site</label>
          <angular2-multiselect 
            [data]="siteOptions" 
            [(ngModel)]="selectedSiteItem" 
            [ngModelOptions]="{standalone: true}"
            [settings]="siteDropdownSettings" 
            (onSelect)="onSiteSelect($event)" 
            (onDeSelect)="onSiteDeSelect()">
          </angular2-multiselect>
        </div>

        <!-- MRN -->
        <div class="col-lg-1 col-md-2 col-sm-6 col-12 mb-3">
          <label for="mrn" class="form-label">MRN</label>
          <input 
            type="text" 
            id="mrn"
            formControlName="mrn"
            class="form-control"
            placeholder="MRN">
        </div>

        <!-- First Name Starts With -->
        <div class="col-lg-1 col-md-2 col-sm-6 col-12 mb-3">
          <label for="firstNameStartsWith" class="form-label">First Name</label>
          <input 
            type="text" 
            id="firstNameStartsWith"
            formControlName="firstNameStartsWith"
            class="form-control"
            placeholder="Starts with...">
        </div>

        <!-- Last Name Starts With -->
        <div class="col-lg-1 col-md-2 col-sm-6 col-12 mb-3">
          <label for="lastNameStartsWith" class="form-label">Last Name</label>
          <input 
            type="text" 
            id="lastNameStartsWith"
            formControlName="lastNameStartsWith"
            class="form-control"
            placeholder="Starts with...">
        </div>

        <!-- Clinician Contains -->
        <div class="col-lg-1 col-md-2 col-sm-6 col-12 mb-3">
          <label for="clinicianContains" class="form-label">Clinician</label>
          <input 
            type="text" 
            id="clinicianContains"
            formControlName="clinicianContains"
            class="form-control"
            placeholder="Contains...">
        </div>

        <!-- Drug Name Contains -->
        <div class="col-lg-1 col-md-2 col-sm-6 col-12 mb-3">
          <label for="drugNameContains" class="form-label">Drug Name</label>
          <input 
            type="text" 
            id="drugNameContains"
            formControlName="drugNameContains"
            class="form-control"
            placeholder="Contains...">
        </div>

        <!-- Missing Records Toggle -->
        <div class="col-lg-1 col-md-2 col-sm-6 col-12 mb-3 d-flex align-items-end">
          <div class="form-check form-switch">
            <input 
              type="checkbox" 
              id="missingRecords"
              formControlName="missingRecords"
              class="form-check-input">
            <label for="missingRecords" class="form-check-label">Missing Records</label>
          </div>
        </div>

      </div>

      <!-- Action Buttons -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="form-actions">
            <button type="submit" class="btn btn-primary me-2">
              <i class="fa fa-search"></i> Submit
            </button>
            <button type="button" class="btn btn-secondary me-2" (click)="resetForm()">
              <i class="fa fa-refresh"></i> Reset
            </button>
            <button 
              type="button" 
              class="btn btn-success" 
              *ngIf="showDownloadButton"
              (click)="downloadReport()">
              <i class="fa fa-download"></i> Download Report
            </button>
          </div>
        </div>
      </div>

    </form>
  </div>
</section>
