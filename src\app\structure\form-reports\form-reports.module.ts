import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';

import { FormReportsComponent } from './form-reports.component';
import { SharedModule } from '../shared/sharedModule';
import { AuthGuard } from '../../guard/auth.guard';

export const routes: Routes = [
  { path: 'form-reports', component: FormReportsComponent, canActivate: [AuthGuard] },
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AngularMultiSelectModule,
    SharedModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes)
  ],
  declarations: [
    FormReportsComponent
  ],
  exports: [TranslateModule]
})
export class FormReportsModule { }
