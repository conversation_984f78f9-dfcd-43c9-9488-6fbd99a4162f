<div class="top-navigation-container">
  <div class="nav-tabs-horizontal">
    <ul class="nav nav-tabs top-level-tabs" role="tablist">
      
      <!-- Dashboard Tab -->
      <li class="nav-item" *ngIf="canShowDashboard()">
        <a 
          class="nav-link" 
          [ngClass]="{'active': activeTab === 'dashboard'}"
          href="javascript: void(0);" 
          role="tab" 
          aria-expanded="true"
          (click)="navigateToTab('dashboard')">
          <i class="fa fa-tachometer" aria-hidden="true"></i>
          <span>Dashboard</span>
        </a>
      </li>

      <!-- Form Reports Tab -->
      <li class="nav-item" *ngIf="canShowFormReports()">
        <a 
          class="nav-link" 
          [ngClass]="{'active': activeTab === 'form-reports'}"
          href="javascript: void(0);" 
          role="tab" 
          aria-expanded="true"
          (click)="navigateToTab('form-reports')">
          <i class="fa fa-file-text-o" aria-hidden="true"></i>
          <span>Form Reports</span>
        </a>
      </li>

    </ul>
  </div>
</div>
