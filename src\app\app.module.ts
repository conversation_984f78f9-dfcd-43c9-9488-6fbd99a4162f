import { BrowserModule } from '@angular/platform-browser';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NgModule ,NgZone,APP_INITIALIZER,Injector,Inject,Injectable} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { Router, NavigationStart, NavigationEnd, RouterModule } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PapaParseModule } from 'ngx-papaparse';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { ApolloModule } from 'apollo-angular';
import { routing } from './app.routing';
import { AppComponent } from './app.component';
import { TopBarComponent } from './components/top-bar/top-bar.component';
import { TopNavigationComponent } from './components/top-navigation/top-navigation.component';
import { BannerAlertTagComponent } from "./structure/manage-alert/banner-alert-tag.component";
// import { unreadmessageFilterPipe } from './structure/inbox/inbox-unreadmessage.pipes';
import { MenuLeftComponent } from './components/menu-left/menu-left.component';
import { MenuSidebarComponent } from './components/menu-sidebar/menu-sidebar.component';
import { AppsMenuComponent } from './structure/intake/apps-menu/apps-menu.component';
// import { AppsMenuComponent } from './structure/eversana/apps-menu/apps-menu.component';
import { MenuRightComponent } from './components/menu-right/menu-right.component';
import { FooterComponent } from './components/footer/footer.component';
import { StructureModule } from './structure/structure.module';
import { NextgenUtilsModule } from './nextgen-utils/nextgen-utils.module';
import { configMultiSiteUrl,configClienturl, configDashboardClientUrl, configFormsApiUrl, configSignatureRequestApiUrl, configSignatureRequestFilingCenterApiUrl, configWpFaqUrl, configMetricsApiUrl, configScheduleFormsApiUrl, configIntroJsGenerateAPIUrl, configWorklistAPiUrl, configPulseAPiUrl, oktaPostLogoutRedirectUri, oktaClassicEngine } from "../environments/environment";
import { TopBarfilterUsersListPipe, SearchRoleFilterPipe, filterUsersListPipe, SearchRoleFilterPatientPipe, SearchFilterRoleTreeViewPipe, SearchFilterRoleTreeViewPatientPipe, virtualStaffFilterPipe } from './components/top-bar/topbar-filterUsersList.pipe';
import { OrderByPipe } from './components/top-bar/topbar-filterUsersList.pipe';
import { filterPatientDiscussionGroupPipe } from './components/top-bar/topbar-filterUsersList.pipe';
import { scheduleSelectionFilterPipe } from './components/top-bar/topbar-filterUsersList.pipe';
import { StructureService } from './structure/structure.service';
import { CrossTenantService } from './structure/shared/cross-tenant.service';
import { SharedModule } from './structure/shared/sharedModule';
import { SharedService } from './structure/shared/sharedServices';
import { SessionService } from './structure/shared/sessionServices';
import { GlobalDataShareService } from './structure/shared/global-data-share.service';
import { AuthGuard } from './guard/auth.guard';
import Elevio from 'elevio/lib/client';
import { AuthService } from './structure/shared/auth.service';
import{DashboardService} from './structure/dashboard/dashboard.service'
import { ToastModule } from '@syncfusion/ej2-angular-notifications';
import { StoreService } from './structure/shared/storeService';

import { Routes } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { ProviderAst } from '@angular/compiler';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { environment } from './../../src/environments/environment'; 
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';
import {
  OKTA_CONFIG,
  OktaAuthModule,
  OktaAuthService
} from '@okta/okta-angular';
import { oktaIssuer, oktaClientId, oktaRedirectUri } from 'environments/environment';
import { FormLandingFlows } from '../app/constants/constants';
import { ROUTES } from './constants/routes';
import { isBlank } from './utils/utils';


declare var NProgress: any;
declare var TelemetryAgent: any;
declare var $: any;
declare var  moduleservice:StructureService;
var sharedServiceData = new SharedService;
const client = new ApolloClient({
  networkInterface: createNetworkInterface({

    uri: configClienturl()
  }).use([{
    applyMiddleware(req, next) {
      if (!req.options.headers) {
        req.options.headers = {};  // Create the header object if needed.
      }
      req.options.headers['apollo-client-name'] = sharedServiceData.apolloClientName;
      req.options.headers['apollo-client-version'] = sharedServiceData.appVersion;
      next();
    },
  }]),
  dataIdFromObject: (object: any) => {
    // Handle admission objects uniquely
    // eslint-disable-next-line no-underscore-dangle
    if (object.__typename === 'Admission' && object.id) {
      return `Admission:${object.id}`;
    }
    // Handle patient objects uniquely, combining patient.id and admission.id
    // eslint-disable-next-line no-underscore-dangle
    if (object.__typename === 'TenantPatientUser' && object.id) {
      const admissionId = object.admission ? object.admission.id : 'no-admission';
      return `Patient:${object.id}:Admission:${admissionId}`;
    }
    // Default fallback for other objects
    // eslint-disable-next-line no-underscore-dangle
    return object.id ? `${object.__typename}:${object.id}` : null;
  }
});
const commonGraphqlClient = new ApolloClient({
  networkInterface: createNetworkInterface({

    uri: configClienturl()
  }).use([{
    applyMiddleware(req, next) {
      if (!req.options.headers) {
        req.options.headers = {};  // Create the header object if needed.
      }
      req.options.headers['apollo-client-name'] = sharedServiceData.apolloClientName;
      req.options.headers['apollo-client-version'] = sharedServiceData.appVersion;
     // passing headers
     if (req.options.headers) {
      const ca: Array<string> = document.cookie.split(';');
      const caLen: number = ca.length;
      const cookieName = `authID=`;
      let c: string;
      for (let i = 0; i < caLen; i += 1) {
        c = ca[i].replace(/^\s+/g, '');
        if (c.indexOf(cookieName) === 0) {
          var token = c.substring(cookieName.length, c.length);
        }
      }
      req.options.headers['authorizationtoken'] = token;
    } 
     //
      next();
    },
  }])
});
export const routes: Routes = [

  //{ path: 'apps/:guid', component: AppsMenuComponent }
  // { path: 'menu/laisan-details', component: LaisanDetailsComponent }
]
// const eversanaClient = new ApolloClient({
//   networkInterface: createNetworkInterface({

//     uri: configEversanaEnrollUrl()
//   }),
// });

const dashboardClient = new ApolloClient({
  networkInterface: createNetworkInterface({

    uri: configDashboardClientUrl()
  }).use([{
    applyMiddleware(req, next) {
      if (!req.options.headers) {
        req.options.headers = {};  // Create the header object if needed.
      }
      req.options.headers['apollo-client-name'] = sharedServiceData.apolloClientName;
      req.options.headers['apollo-client-version'] = sharedServiceData.appVersion;
      next();
    },
  }])
});
const multiSite = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configMultiSiteUrl()
  }).use([{
    applyMiddleware(req, next) {
      if (!req.options.headers) {
        req.options.headers = {};  // Create the header object if needed.
      }
      req.options.headers['apollo-client-name'] = sharedServiceData.apolloClientName;
      req.options.headers['apollo-client-version'] = sharedServiceData.appVersion;
      const ca: Array<string> = document.cookie.split(';');
      const caLen: number = ca.length;
      const cookieName = `authID=`;
      let c: string;
      for (let i = 0; i < caLen; i += 1) {
        c = ca[i].replace(/^\s+/g, '');
        if (c.indexOf(cookieName) === 0) {
          var token = c.substring(cookieName.length, c.length);

        }
      }
      req.options.headers['authorizationtoken'] = token;
      next();
    },
  }])
});


const introjsGenerateClient = new ApolloClient({
  networkInterface: createNetworkInterface({

    uri: configIntroJsGenerateAPIUrl()
  }),
});

// const enrollClient = new ApolloClient({
//   networkInterface: createNetworkInterface({
//     uri: configEnrollClientUrl() + '/graphql/'
//   }),
// });
const signatureRequestApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configSignatureRequestApiUrl()
  }).useAfter([{
    applyAfterware({ response }, next) {
      const res = response.clone()
      console.log("responsevvvvvvvvvvvvvvvvvv");
      console.log(res);
      // handle apollo errors
      res.json().then(json => {
      console.log("error===========> ");
      console.log(json);
      if (json && json.errors && json.errors.length) {
        console.log(json.errors[0])
        var eventCustom = new CustomEvent(
          "graphQlError", 
          {
            detail: {
              message: json.errors[0].message,
            },
            bubbles: true,
            cancelable: true
          }
        );
        document.dispatchEvent(eventCustom);
      }else{
        next()
      }
    })
    }
  }])  
});

const scheduledFormsApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configScheduleFormsApiUrl()
  }),
  dataIdFromObject: (object: any) => {
    // eslint-disable-next-line no-underscore-dangle
    switch (object.__typename) {
      case 'ScheduledDetailsPaginate':
        return object.id;
      case 'UserTypePaginate':
        // eslint-disable-next-line no-underscore-dangle
        return object.admissionName ? `${object.__typename}:${object.id}:${object.admissionName}` : object.id;
      default:
        return object.id; // Default fallback
    }
  }
});

const metricsApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configMetricsApiUrl()
  }),
});

const FormsApiUrl = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configFormsApiUrl()
  }),
});

const SignatureRequestFilingCenterApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configSignatureRequestFilingCenterApiUrl()
  }),
});

const wpFaqGraphApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configWpFaqUrl()
  }),
});

const worklistGraphApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configWorklistAPiUrl()
  }),
});
const pulseWorklistGraphApi = new ApolloClient({
  networkInterface: createNetworkInterface({
    uri: configPulseAPiUrl()
  }).use([{
    applyMiddleware(req, next) {
      if (!req.options.headers) {
        req.options.headers = {};  // Create the header object if needed.
      }
      const ca: Array<string> = document.cookie.split(';');
      const caLen: number = ca.length;
      const cookieName = `authID=`;
      let c: string;
      for (let i = 0; i < caLen; i += 1) {
        c = ca[i].replace(/^\s+/g, '');
        if (c.indexOf(cookieName) === 0) {
          var token = c.substring(cookieName.length, c.length);
          
        }
      }
      req.options.headers['authorizationtoken'] = token;
      next();
    },
  }])
});

const oktaConfig = {
  issuer: oktaIssuer,
  clientId: oktaClientId,
  redirectUri: oktaRedirectUri,
  useClassicEngine: oktaClassicEngine,
  scopes: ['openid', 'profile', 'email'],
  authParams: {
    responseType: ['token', 'id_token'],
    pkce: false,
    display: 'page',
    loggingLevel: 'debug'
  },
  tokenManager: {
    autoRenew: true,
    expireEarlySeconds: 30
  }
};

export function provideClients() {
  return {
    default: client,
    signatureRequestApi: signatureRequestApi,
    scheduledFormsApi: scheduledFormsApi,
    metricsApi: metricsApi,
    signatureRequestFilingCenterApi: SignatureRequestFilingCenterApi,
    FormsApiUrl: FormsApiUrl,
    dashboardClient: dashboardClient,
    multiSite: multiSite,
    commonGraphqlClient: commonGraphqlClient,
    introjsGenerateClient: introjsGenerateClient,
    // enrollClient: enrollClient,
    wpFaqGraphApi: wpFaqGraphApi,
    worklistGraphApi: worklistGraphApi,
    pulseWorklistGraphApi: pulseWorklistGraphApi
  };
}

// Add translation file loading 
export function createTranslateLoader(http: HttpClient): TranslateHttpLoader {
    return new TranslateHttpLoader(http, './assets/i18n/', '.json?v=' + sharedServiceData.appVersion);
}

@NgModule({
    declarations: [
        AppComponent,
        TopBarComponent,
        TopNavigationComponent,
        MenuLeftComponent,
    MenuSidebarComponent,
        AppsMenuComponent,
        MenuRightComponent,
       FooterComponent,
       BannerAlertTagComponent,
        TopBarfilterUsersListPipe,
        virtualStaffFilterPipe,
        SearchRoleFilterPipe,
        filterUsersListPipe,
        SearchFilterRoleTreeViewPipe,
        SearchFilterRoleTreeViewPatientPipe,
        SearchRoleFilterPatientPipe,
  		// unreadmessageFilterPipe
        OrderByPipe,
        filterPatientDiscussionGroupPipe,
        scheduleSelectionFilterPipe
    ],
    imports: [
        BrowserModule,
        AngularMultiSelectModule,
        FormsModule,
        ReactiveFormsModule,
        ApolloModule.forRoot(provideClients),
        HttpModule,
        HttpClientModule,
        RouterModule.forChild(routes),
        StructureModule,
        NextgenUtilsModule,
        NgbModule.forRoot(),
        routing,
        SharedModule,
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: (createTranslateLoader),
                deps: [HttpClient]
            }
        }),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: !environment.production ? NgxLoggerLevel.TRACE : NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        }),
        PapaParseModule,
        ToastModule,
        NgIdleKeepaliveModule.forRoot(),
        OktaAuthModule
        //SignaturePadModule
    ],
    providers: [TopBarfilterUsersListPipe, SearchRoleFilterPipe,SearchRoleFilterPatientPipe, 
      filterUsersListPipe, SearchFilterRoleTreeViewPipe, SearchFilterRoleTreeViewPatientPipe,StoreService,
      OrderByPipe, SharedService, SessionService, GlobalDataShareService, AuthGuard,CrossTenantService, virtualStaffFilterPipe,AuthService, DashboardService,
      {
        provide: APP_INITIALIZER,
        useFactory: resourceProviderFactory,
        deps: [SessionService,SharedService],
        multi: true
        },
        { provide: OKTA_CONFIG, useValue: oktaConfig }
      ],
    bootstrap: [ AppComponent ]
})

export class AppModule { 
   
  prevUrl: any = ''
  constructor(private router: Router,
    public _structureService: StructureService,
    public _sharedService:SharedService,
    private _ngZone: NgZone,
    private _idpAuth:AuthService,
    private oktaAuth: OktaAuthService
  ) { 
    if (this._structureService.environment == 'portal') {
      console.log = function () { }
    }
    this._structureService.subscribeSocketEvent('userPopUpMessageTO').subscribe((messageData) => {
          var message= messageData.message;
            var notify = $.notify(message);
            setTimeout(function() {
                notify.update({'type': 'danger', 'message': '<strong>'+message+'</strong>'});
            }, 1000);
    });
    router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        var currentUrl = event.url;
        localStorage.setItem('currentUrl', currentUrl);
        console.log("currentUrl===========> "+currentUrl);
        console.log("help center",Elevio)
        if(Elevio)
        {
          // $('#_elev_io').hide();
          // Elevio.close();
          this._ngZone.runOutsideAngular(() => {
            this._ngZone.run(() => { 
            console.log("wwwwwwwwwwwwwwwwwwwwwwwwwwww");
          Elevio.disable();
          });
          });
        console.log("help center outside",(window as any)._elev,currentUrl);
        if((window as any)._elev && (window as any)._elev != undefined){
          (window as any)._elev.on('load', function(_elev) {
            console.log("help center inside",(window as any)._elev);
            if(_elev && _elev != undefined && _elev.widgetIsOpen() && _elev.widgetIsOpen() == true)
            {
                console.log("help center",_elev.widgetIsOpen());
                _elev.close();
            }
          });
        }
        /*if (currentUrl != '/login' && currentUrl != '/registration' && !currentUrl.includes('/reset-password') && localStorage.getItem('userDetails') == null) {
          this.router.navigate(['/login']);
        }*/
      }
      }

      if (event instanceof NavigationEnd) {
        setTimeout(function () {
          //NProgress.done();
        }, 200);
      }

      if (event instanceof NavigationEnd) {
        var currentUrl = event.url;     
        console.log("currentUrl===========> "+currentUrl);
        
        console.log('userDetails-->'+this._structureService.userDetails);
        let userData:any;
        if(this._structureService.userDetails){
           userData = JSON.parse(this._structureService.userDetails); 
        }
        console.log("userData===>",userData);


        if(!currentUrl.includes("/pages") && (this._structureService.ssoBrandingURLChecking() ||this._structureService.isTokenOrCodeinURL) && (!userData || userData.userId=='')){
          console.log("idp");
          this._structureService.idpWorkflow=true;
          this._sharedService.idpLoginMessage.emit({idpWorkflow:true});
          var idpAuthUrl = window.location.origin;
          const idpAuthUrlOrginal = `${idpAuthUrl}${this._structureService.getSSOUrl}`;
          const url = `${idpAuthUrl}${this._structureService.getSSOUrl}`;
          //console.log("idpAuthUrlOrginal====> "+idpAuthUrlOrginal);
          //console.log("this._structureService.idpTenantConfigDetails====>",this._structureService.idpTenantConfigDetails);
          //console.log("currentUrl.includes('/id_token')=====>",currentUrl.includes("id_token"));
          if(this._structureService.idpTenantConfigDetails && this._structureService.isTokenOrCodeinURL){
            console.log("configuration allready set and check for token");
            const idpDetails = this._structureService.idpTenantConfigDetails.data;
              //var url =idpAuthUrlOrginal.replace("/#/performsp",'/performsp');
              const settings:any={
                authority: idpDetails.consolo_baseurl,
                client_id: idpDetails.consolo_clientId,
                response_type: idpDetails.response_type,
                redirect_uri: url,
                scope:idpDetails.consolo_scope
              }
            this._idpAuth.idpConfigurationSet(settings);
            this._idpAuth.userLogginCheck();
            this._idpAuth.getUser().then((user:any)=>{
              //console.log("idp get user---> ",user);
            });
          }else if(this._structureService.ssoBrandingURLChecking(true)){
            console.log("configuration not set call api for configuration----");
            this._sharedService.idpLoginMessage.emit({message:"Configuration fetching..."});
            setTimeout(() => {
            this._structureService.idpTenantConfigCheck(idpAuthUrlOrginal).then((data:any) => {
              console.log("idpTenantConfigCheck response=====",data);
              if(!data.data) {
                $.notify({ 'message': '<strong>There is no configuration associated with the requested URL.</strong>' }, { delay: 0, type: 'danger' });
                this._sharedService.idpLoginMessage.emit({idpWorkflow:false})
                ;return;}
              const idpDetails = data.data;
             
              const settings:any={
                authority: idpDetails.consolo_baseurl,
                client_id: idpDetails.consolo_clientId,
                response_type: idpDetails.response_type,
                redirect_uri: url,
                scope:idpDetails.consolo_scope
              }
              console.log("idpTenantConfigCheck==> settings==> ",settings);
              this._sharedService.idpLoginMessage.emit({message:"Idp Login details fetching..."});
              this._idpAuth.idpConfigurationSet(settings);
              this._idpAuth.userLogginCheck();
              this._idpAuth.endSigninMainWindow().then((user:any)=>{
                console.log( "idp endSigninMainWindow then",user);
                if(user){
                  this._structureService.idpUserLogin(user.id_token).then((data) => {
                    console.log("idp endSigninMainWindow idpUserLogin then",data);
                    // this._sharedService.idpLoginSuccess.emit(data);
                  });
                }
                else if(!user && window.location.href.includes('code')){
                  const codeDetails = this._structureService.checkAuthorizationFlow();
                  this._structureService.idpUserLogin(codeDetails.code,codeDetails.tokenEndpoint).then((data) => {
                    console.log("idp endSigninMainWindow idpUserLogin then",data);
                  });
                }else{
                  this._idpAuth.startSigninMainWindow(this._sharedService.ssoEmailId);
                  this._sharedService.ssoEmailId = '';
                }
                      if (!user && window.location.href.includes('id_token')) {
                        localStorage.setItem('sso_id_token', window.location.href.split('id_token=')[1].split('&')[0]);
                      }
                    })
                    .catch(() => {
                //console.log("endSigninMainWindow errorrrrrrr",error);
                this._sharedService.idpLoginMessage.emit({idpWorkflow:false});
              })
            }).catch((ex) => {
              console.log("idpTenantConfigCheck catch ex==>",ex);
              $.notify({ 'message': '<strong>Idp configuration fetch error</strong>' }, { delay: 0, type: 'danger' });
                this._sharedService.idpLoginMessage.emit({idpWorkflow:false});
            });
          }, 0);
          }
           /* }
          }).catch((error)=>{
            console.log("endSigninMainWindow errorrrrrrr",error);
          })*/
          return true;
        }else{
          //console.log("else......................");
          //console.log("else......................");
          //console.log("else......................");
          //console.log("else......................");
        //}
          if(!currentUrl.includes('/signature/signature-requests/')){
            //console.log("applayDatatablesaveState = signature/signature-requests false==========")
            if(currentUrl != '/signature/signature-requests-list' && currentUrl != '/signature/signature-requests'){
              //console.log("currentUrl===> applayDatatablesaveState = false")
              this._sharedService.applayDatatablesaveState = false;
            }
          }
          
          if (currentUrl !="/id_token" && currentUrl !="/code" && currentUrl != '/login' && currentUrl != '/registration' && !currentUrl.includes('/reset-password') && this._structureService.userDetails == null) {
            //console.log(" login redirect enter..");
            this.router.navigate(['/login']);
          }
          else if (currentUrl.includes("/account/account-settings/")) {
            //console.log("account settings.....")
            
            const userData = this._structureService.userDetails? JSON.parse(this._structureService.userDetails):{};
            if (!userData.privileges.includes("manageTenants")) {
              this.router.navigate(['/inbox']);
            }
          }else if((this._structureService.ssoBrandingURLChecking() ||currentUrl.includes("/id_token") ||currentUrl.includes("/code")) && userData.userId !=''){
            console.log("perform sp......",userData.defaultPage);
            let brightreeFlowRoute;
            if (!isBlank(localStorage.getItem('brightreeFlowRoute')) && localStorage.getItem('isBrightreeFlow') === 'true') {
              brightreeFlowRoute = localStorage.getItem('brightreeFlowRoute');
              this.router.navigate([brightreeFlowRoute || userData.defaultPage]);
            } else {
            this.router.navigate([userData.defaultPage || '/profile']);
            }
          }
          else if (currentUrl != '/chat') {
            var activityData = {
              activityName: "Page Navigation",
              activityType: "page access",
              activityDescription: "Current Page - " + this.prevUrl + ". New Page - " + currentUrl.replace('/', '')
            };
            var patt = new RegExp(/faq\/+[A-Za-z]+/g);

            if (currentUrl == '/faq' || patt.test(currentUrl)) {
              if (currentUrl == '/faq') {
                var faqHeading = 'troubleshoot-infusion';
              }
              else {
                var faqHeading = this.router.url.substring(this.router.url.indexOf("/faq/") + 5, this.router.url.length);
              }
              currentUrl = faqHeading;
              faqHeading = faqHeading.replace(/-/g, " ");
              faqHeading = faqHeading.charAt(0).toUpperCase() + faqHeading.slice(1);
              activityData.activityName = "FAQ Visits - " + faqHeading;
              activityData.activityDescription = "Current Page - " + this.prevUrl + ". New Page - " + currentUrl

            }

            this.prevUrl = currentUrl.replace('/', '');
            this._structureService.trackActivity(activityData);
          }
        }
      }
    //   this._structureService.displayProgress.subscribe(
    //     (displayProgress) => {
    //     console.log("hiiiii",this._structureService.displayProgress);
    //       this.menuShow = false
    //       console.log(this.menuShow)
    //   })
    });
    this.oktaAuth.getTokenManager().on('expired', async (key, expiredToken) => {
      console.log('Token with key', key, ' has expired:');
      console.log(expiredToken);
      try {
        await this.oktaAuth.getAccessToken();
        await this.oktaAuth.getIdToken();
        this._structureService.updateOktaAccessToken();
      } catch (error) {
        console.error('Error renewing token:', error);
      }
    });
    this.oktaAuth.getTokenManager().on('error', (err) => {
        this.oktaAuth.logout({
          postLogoutRedirectUri: oktaPostLogoutRedirectUri,
        });
        setTimeout(() => {
          localStorage.clear();
          sessionStorage.clear();
          this.router.navigate(['/login']);
        }, 500);
    });
   
  }
  
}


export function resourceProviderFactory(provider: SessionService,_sharedService:SharedService) { 

  const url = new URL(document.URL).searchParams;
  let formLandingFlow = url.get('formLandingFlow');
  const formGuid = url.get('formGuid');
  if (formGuid) {
    localStorage.setItem('formGuid', formGuid);
  }
  const externalPatientId = url.get('externalPatientId');
  if (externalPatientId) {
    localStorage.setItem('externalPatientId', externalPatientId);
    formLandingFlow = FormLandingFlows.FORM_LANDING;
  }
  if (
    formLandingFlow === FormLandingFlows.MANAGE_FORM ||
    formLandingFlow === FormLandingFlows.FORM_TYPES ||
    formLandingFlow === FormLandingFlows.COMPLETE_FORM ||
    formLandingFlow === FormLandingFlows.FORM_LANDING
  ) {
    localStorage.setItem('brightree_formLandingFlow', formLandingFlow || '');
    localStorage.setItem('isBrightreeFlow', 'true');
    localStorage.setItem('isBrightreeFlowRouteSession', 'true');
    const flowRoutes = {
      [FormLandingFlows.MANAGE_FORM]: ROUTES.manageForms,
      [FormLandingFlows.FORM_TYPES]: ROUTES.formTypes,
      [FormLandingFlows.COMPLETE_FORM]: ROUTES.formList,
      [FormLandingFlows.FORM_LANDING]: ROUTES.formLanding
    };
    if (flowRoutes[formLandingFlow]) {
      localStorage.setItem('brightreeFlowRoute', flowRoutes[formLandingFlow]);
    }
    // Redirect to clean /brightree URL
    if (isBlank(localStorage.getItem('authenticationToken'))) {
      window.location.href = '/brightree';
    return () => Promise.resolve();
    }
  }
  if (
    window.location.pathname === '/brightree' &&
    window.location.search === '' &&
    localStorage.getItem('isBrightreeFlowRouteSession') !== 'true' &&
    isBlank(formLandingFlow)
  ) {
    // this console log is for debugging purposes to see if the flow data is being cleared and will be removed after testing
    // eslint-disable-next-line no-console
    console.log('[brightree] Clearing Brightree flow data');
    localStorage.removeItem('brightree_formLandingFlow');
    localStorage.removeItem('isBrightreeFlow');
    localStorage.removeItem('brightreeFlowRoute');
  }
  const token = url.get('authtoken'); 
  if(token){
    document.cookie.split(";").forEach(function(c) { document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); });
    localStorage.clear();     
    localStorage.setItem('authenticationToken',decodeURIComponent(token))
  }  
  const authToken = localStorage.getItem('authenticationToken');
  var currentUrl = localStorage.getItem("currentUrl");
  if(currentUrl == null)
  {
    currentUrl = '';
  }
  
  if(authToken){

    return () => provider.getSessionData().then(
      (data) => {
        console.log('getSessionTenant'+ JSON.stringify(data));
        console.log('currentUrl-->'+currentUrl);
        if((!data) || (!data['response'] )){  
          if(data['status']== '505' || data['status']== 505 ){             
            var notify = $.notify('Your session has timed out!');
            setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>Your session has timed out!</strong>'});
            },100000);
            localStorage.setItem('logoutSessionOut', "true");
           }        
            console.log('currentUrl-->'+currentUrl);
            if(currentUrl !== '/reset-password/enrol'){
              provider._structureService.deleteCookie('authenticationToken');
            }
            if(currentUrl !== '/' && currentUrl !== '/login' && currentUrl !== '/reset-password/enrol' && !currentUrl.includes('reset-password')){
              provider._router.navigate(['/login']);
            }           
            provider._structureService.logout(false,false);
          }
        else {
            const { response } = data as any;
            let brightreeFlowRoute;
            if (!isBlank(localStorage.getItem('brightreeFlowRoute')) && localStorage.getItem('isBrightreeFlow') === 'true') {
              brightreeFlowRoute = localStorage.getItem('brightreeFlowRoute');
            }

            const retUserSuccess = provider.userloginSuccess(response, brightreeFlowRoute || '');
            if (retUserSuccess) {
              provider.loginSuccess(retUserSuccess);
            } else {
              var notify = $.notify('Invalid username or password');
              setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>Invalid username or password</strong>'});
              }, 1000);
            }
        
        }
        provider.callTelemetryAgentLoad();
      },err =>{
         
        if(currentUrl !== '/reset-password/enrol'){
          provider._structureService.deleteCookie('authenticationToken');
        }
        if(currentUrl !== '/' && currentUrl !== '/login' && currentUrl !== '/reset-password/enrol'){
          provider._router.navigate(['/login']);
        }
        provider.callTelemetryAgentLoad();

      })

  }else{   
    return()=> {
      if (currentUrl !== '/' && currentUrl !== '/login' && currentUrl !== '/registration' && !currentUrl.includes('reset-password') && currentUrl!="" && !provider.ssoBrandingURLChecking()) {
        console.log('login');
        provider._structureService.logout();
        }  
      if(currentUrl == '/registration') {
                provider._router.navigate(['/registration']);                  
      }
      provider.callTelemetryAgentLoad();
     };                                 
    }

  }




