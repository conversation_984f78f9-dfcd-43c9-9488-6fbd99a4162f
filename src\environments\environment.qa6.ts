export const apiCoreBaseUrl = 'https://api-core-qa6.citushealth.com'; // Java APIs Base Endpoint
const serverBaseUrl = 'https://portal-qa6.citushealth.com';
const machFormBaseUrl = 'https://forms-qa6.citushealth.com/';
const apiBaseUrl = serverBaseUrl + '/cometchat/';
const loginApiUrl =
  apiBaseUrl + 'citus-health/v4/chat-window-authenticate-forgerock.php';
const socketUrl =
  apiBaseUrl + 'citus-health/v4/chat-window-authenticate-forgerock.php';
const idpClientUrl =
  apiBaseUrl + 'citus-health/v4/chat-window-wrapper-authenticate-forgerok.php';
const iconPath = serverBaseUrl + '/webapp/www/img/';
const sentPushNotificationApi =
  apiBaseUrl + 'citus-health/v4/push-notification.php';
const pushServerAPIUrl = 'https://api.push-notification-qa6.citushealth.com/';
const socketIo = 'https://portal-socket-qa6.citushealth.com/';
const socketConnectionUrl = socketIo + 'socket.io/socket.io.js';
const clientUrl = 'https://gqdss-qa6.citushealth.com/graphql';
const eversanaEnrollUrl =
  'https://middleware-gqdss-qa6.citushealth.com/eversana/graphql';
const introJsGenerateAPIUrl =
  'https://middleware-gqdss-qa6.citushealth.com/generate/intro/graphiql';
const signatureRequestApiUrl =
  'https://middleware-gqdss-qa6.citushealth.com/graphql';
const scheduleFormsApiUrl =
  'https://middleware-gqdss-qa6.citushealth.com/automation/graphiql';
const formsApiUrl =
  'https://middleware-gqdss-qa6.citushealth.com/forms/graphql';
const metricsApiUrl = 'https://gqdss-qa6.citushealth.com/dashboard/graphql';
const signatureRequestFilingCenterApiUrl =
  'https://middleware-gqdss-qa6.citushealth.com/sync/graphql';
const dashboardClientUrl =
  'https://gqdss-qa6.citushealth.com/dashboard/graphql';
const releaseStage = 'Testing';
const version = 'v4';
const platform = 'Desktop App';
const serverEnvironment = 'test';
const timeZone = 5.5;
const domainName = 'Test';
const activityTrackingApi = apiBaseUrl + '/citus-health/v4/track-activity.php';
const wpFaqUrl = 'https://devl.cms.citushealth.com/call-bell-app/graphql/';
const locationApiUrl = 'http://ipinfo.io';
const wpMiddlewareWebHook = 'https://api-qa6.citushealth.com/';
const worklistAPiUrl =
  'https://middleware-gqdss-qa6.citushealth.com/forms/graphql';
const pulseAPiUrl =
  'https://middleware-gqdss-qa6.citushealth.com/intake/graphql';
const educationMaterialFileUrl =
  'https://portal-qa6.citushealth.com/cometchat/writable/filetransfer/uploads/education-materials';
const assetsUrl = 'https://assets-qa6.citushealth.com';
const visitScheduleFileUrl =
  apiBaseUrl + 'writable/filetransfer/uploads/visit-schedule';
const multiSiteUrl = 'https://gqdss-qa6.citushealth.com/multisite/graphql';
const visitScheduleServiceUrl = 'https://visit-schedule-qa6.citushealth.com';
export const agGridLicenseKey =
  'Evaluation_License-_Not_For_Production_Valid_Until_28_March_2019__MTU1MzczMTIwMDAwMA==555b6946f1f4ee082a509e13fbc32c10';
//'CompanyName=Citus Health,LicensedApplication=app.citushealth.com,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=1,AssetReference=AG-007525,ExpiryDate=25_March_2021_[v2]_MTYxNjYzMDQwMDAwMA==a3930ca04014febb4b40828ebcbc1bbf';
//  'Citus_Health___app.citushealth.com_1Devs_1Deployment_30_January_2020__MTU4MDM0MjQwMDAwMA==54177bb4c36796d743d2c992fa28cf83';
export const defaultCountryFlag = 'us';
export const oktaFlow = true;
export const oktaClassicEngine = true;
export const oktaBaseUrl = 'https://login-qa.citushealth.com';
export const oktaIssuer = `${oktaBaseUrl}/oauth2/aus23980h1beaxq5F0h8`;
export const oktaClientId = '0oa2397jm8iuo9Lkc0h8';
export const oktaRedirectUri = 'https://app-qa6.citushealth.com/login/callback';
export const oktaPostLogoutRedirectUri = 'https://app-qa6.citushealth.com/login';
export const microFrontendUrl = 'https://172.20.176.1:4245';
export const environment = {
  production: false,
};

export function configReleaseStage() {
  return releaseStage;
}

export function configLoginApi() {
  return loginApiUrl;
}

export function configLocationApi() {
  return locationApiUrl;
}

export function configBaseUrl() {
  return apiBaseUrl;
}

export function configServerBaseUrl() {
  return serverBaseUrl;
}

export function configSocketUrl() {
  return socketUrl;
}
export function configIdpClientUrl() {
  return idpClientUrl;
}

export function configactivityTrackingApi() {
  return activityTrackingApi;
}

export function configIconPath() {
  return iconPath;
}

export function configVersion() {
  return version;
}

export function configPlatform() {
  return platform;
}

export function configEnvironment() {
  return serverEnvironment;
}
export function configTimeZone() {
  return timeZone;
}

export function configDomainName() {
  return domainName;
}
export function configFormsApiUrl() {
  return formsApiUrl;
}

export function configSocketIo() {
  return socketIo;
}

export function configSocketConnectionUrl() {
  return socketConnectionUrl;
}

export function configSentPushNotificationApi() {
  return sentPushNotificationApi;
}

export function configPushServerAPIUrl() {
  return pushServerAPIUrl;
}

export function configClienturl() {
  return clientUrl;
}

export function configEversanaEnrollUrl() {
  return eversanaEnrollUrl;
}
export function configIntroJsGenerateAPIUrl() {
  return introJsGenerateAPIUrl;
}

export function configSignatureRequestApiUrl() {
  return signatureRequestApiUrl;
}

export function configScheduleFormsApiUrl() {
  return scheduleFormsApiUrl;
}

export function configMetricsApiUrl() {
  return metricsApiUrl;
}

export function configSignatureRequestFilingCenterApiUrl() {
  return signatureRequestFilingCenterApiUrl;
}

export function configDashboardClientUrl() {
  return dashboardClientUrl;
}
export function configWpFaqUrl() {
  return wpFaqUrl;
}

export function configWpMiddlewareWebHook() {
  return wpMiddlewareWebHook;
}

// export function configEnrollClientUrl() {
//   return enrollClientUrl;
// }
export function configWorklistAPiUrl() {
  return worklistAPiUrl;
}

export function configPulseAPiUrl() {
  return pulseAPiUrl;
}

export function configEducationMaterialUrl() {
  return educationMaterialFileUrl;
}
export function configAssetsUrl() {
  return assetsUrl;
}
export function configVisitScheduleFileUrl() {
  return visitScheduleFileUrl;
}

export function configMultiSiteUrl() {
  return multiSiteUrl;
}
export function configMachFormUrl() {
  return machFormBaseUrl;
}
export function configVisitScheduleServiceUrl(){
	return visitScheduleServiceUrl;
}
