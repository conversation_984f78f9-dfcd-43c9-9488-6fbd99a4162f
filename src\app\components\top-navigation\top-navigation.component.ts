import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { StructureService } from '../../structure/structure.service';

@Component({
  selector: 'app-top-navigation',
  templateUrl: './top-navigation.component.html',
  styleUrls: ['./top-navigation.component.css']
})
export class TopNavigationComponent implements OnInit {
  activeTab: string = '';
  userData: any = {};
  privileges: any = {};

  constructor(
    private router: Router,
    private structureService: StructureService
  ) {}

  ngOnInit(): void {
    this.userData = this.structureService.getCookie('userData') || {};
    this.privileges = this.structureService.getCookie('userPrivileges') || {};
    
    // Set active tab based on current route
    this.setActiveTab();
    
    // Listen to route changes
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.setActiveTab();
      }
    });
  }

  setActiveTab(): void {
    const currentUrl = this.router.url;
    if (currentUrl.includes('/dashboard')) {
      this.activeTab = 'dashboard';
    } else if (currentUrl.includes('/form-reports')) {
      this.activeTab = 'form-reports';
    } else {
      this.activeTab = '';
    }
  }

  navigateToTab(tab: string): void {
    this.activeTab = tab;
    switch (tab) {
      case 'dashboard':
        this.router.navigate(['/dashboard']);
        break;
      case 'form-reports':
        this.router.navigate(['/form-reports']);
        break;
    }
  }

  canShowDashboard(): boolean {
    return this.privileges && this.privileges.showDashboard;
  }

  canShowFormReports(): boolean {
    // For now, show to all authenticated users
    // Can be modified to check specific privileges later
    return true;
  }
}
