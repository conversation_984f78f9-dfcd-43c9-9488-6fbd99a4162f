/* Top Navigation Component Styles */

.top-navigation-container {
  background-color: #ff0000 !important; /* Temporary red background for debugging */
  border-bottom: 1px solid #e4e9f0;
  padding: 20px !important; /* Temporary large padding for debugging */
  margin: 0;
  position: relative;
  z-index: 100;
  min-height: 60px !important; /* Ensure it has height */
}

.nav-tabs-horizontal {
  margin: 0;
  padding: 0;
}

.top-level-tabs {
  border-bottom: 1px solid #e4e9f0;
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
}

.top-level-tabs .nav-item {
  margin-bottom: -1px;
  cursor: pointer;
}

.top-level-tabs .nav-item .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  padding: 12px 20px;
  color: #74708d;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border-radius: 0;
}

.top-level-tabs .nav-item .nav-link:hover {
  border-bottom-color: #b8beca;
  color: #0190fe;
  background-color: rgba(1, 144, 254, 0.05);
}

.top-level-tabs .nav-item .nav-link.active {
  border-bottom-color: #0190fe !important;
  color: #0190fe;
  background-color: #fff;
  font-weight: 600;
}

.top-level-tabs .nav-item .nav-link i {
  font-size: 1rem;
}

.top-level-tabs .nav-item .nav-link span {
  font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .top-level-tabs {
    padding: 0 10px;
  }
  
  .top-level-tabs .nav-item .nav-link {
    padding: 10px 15px;
    font-size: 0.9rem;
  }
  
  .top-level-tabs .nav-item .nav-link span {
    display: none;
  }
  
  .top-level-tabs .nav-item .nav-link i {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .top-level-tabs {
    padding: 0 5px;
  }
  
  .top-level-tabs .nav-item .nav-link {
    padding: 8px 12px;
  }
}

/* Integration with existing styles */
.top-navigation-container + .cat__content {
  padding-top: 0;
}

/* Ensure proper z-index layering */
.top-navigation-container {
  position: relative;
  z-index: 99;
}

/* Match existing application styling */
.nav-tabs-horizontal .nav-tabs {
  border-bottom: 1px solid #e4e9f0;
}

.nav-tabs-horizontal .nav-tabs .nav-item {
  margin-bottom: -2px;
  cursor: pointer;
}

.nav-tabs-horizontal .nav-tabs .nav-item .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
}

.nav-tabs-horizontal .nav-tabs .nav-item .nav-link.active, 
.nav-tabs-horizontal .nav-tabs .nav-item .nav-link:focus {
  border-bottom-color: #0190fe !important;
}

.nav-tabs-horizontal .nav-tabs .nav-item .nav-link:hover {
  border-bottom-color: #b8beca;
}
