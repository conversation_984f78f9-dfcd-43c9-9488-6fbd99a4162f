import { DocumentCenterService } from 'app/services/document-center/document-center.service';
import { FileValidationService } from 'app/services/file-validation/file-validation.service';
import { MessageService } from 'app/services/message-center/message.service';
import { TranslateModule } from '@ngx-translate/core';
import { NgUploaderModule } from 'ngx-uploader';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TagService } from 'app/services/tag/tag.service';
import { SearchService } from 'app/services/search-criteria/search.service';
import { ActivityLogsModule } from './activity-logs/activity-logs.module';
import { AFSModule } from './afs/afs.module';
import { CampaignManagerModule } from './campaign-manager/campaign-manager.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { ProfileModule } from './profile/profile.module';
import { RegitrationModule } from './registration/registration.module';
import { PagesModule } from './pages/pages.module';
import { AccountModule } from './account/account.module';
import { UsersModule } from './users/users.module';
import { SignaturesModule } from './signatures/signatures.module';
import { SuppliesModule } from './supplies/supplies.module';
import { InboxModule } from './inbox/inbox.module';
import { MessageModule } from './message/message.module';
import { EducationModule } from './education/education.module';
import { ManageAlertModule } from './manage-alert/manage-alert.module';
import { MaskedMessageModule } from './masked-message/masked-message.module';
import { ScheduleModule } from './schedules/schedule.module';
import { VisitSchedulerModule } from './visit-scheduler/visit-scheduler.module';
import { VisitScheduleModule } from './visit-schedule/visit-scheduler.module';
import { LoginModule } from './login/login.module';
import { OpsToolsModule } from './ops-tools/ops-tools.module';
import { DocumentsModule } from './documents/documents.module';
import { VerbalOrderModule } from './verbal-order/verbal-order.module';
import { InfusionSupportModule } from './infusion-support/infusion-support.module';
import { ArchiveModule } from './archieved-messages/archive.module';
import { FillingCenterManagerModule } from './file-manager/filling-center-manager.module';
import { FormManageModule } from './forms/forms.module';
import { ExternalIntegrationModule } from './external-integration/external-integration.module';
import { UserRegistrationModule } from './user-registration/user-registration.module';
import { StructureService } from './structure.service';
import { ToolTipService } from './tool-tip.service';
import { videoTutorialService } from './video-tutorial.service';
import { RegistrationService } from './registration/registration.service';
import { InboxService } from './inbox/inbox.service';
import { ArchiveService } from './archieved-messages/archive.service';
import { ChatService } from './inbox/chat.service';
import { SignService } from './signatures/sign.service';
import { ScheduleService } from './schedules/schedule.service';
import { VisitSchedulerService } from './visit-scheduler/visit-scheduler.service';
import { VisitScheduleService } from './visit-schedule/visit-scheduler.service';
import { MessageBroadcastService } from './message/message-broadcast.service';
import { MaskedMessageService } from './masked-message/masked-message.service';
import { MessageViewerService } from './message/message-viewer.service';
import { FormsService } from './forms/forms.service';
import { WorkListModule } from './worklists/worklist.module';
import { RoutingScheduleService } from './account/manage-routing-rules/routing-schedule-calendar/routing-schedule.service';
import { WorkListService } from './worklists/worklist.service';
import { WorklistIndexdbService } from './worklists/worklist-indexdb.service';
import { EversanaWorkListService } from './eversana/eversana-worklist/worklist.service';
import { PatientActivityHubModule } from './patient-activity-hub/patient-activity-hub.module';
import { AddUserModule } from './add-user/add-user.module';
import { EversanaModule } from './eversana/eversana.module';
import { ManageSitesModule } from './manage-sites/manage-sites.module';
import { ManageSitesService } from './manage-sites/manage-sites.service';
import { ManageRoutingRulesService } from './account/manage-routing-rules/manage-routing-rules.service';
import { IntakeModule } from './intake/intake.module';
import { BookedSchedulerModule } from './booked-scheduler/booked-scheduler.module';
import { MasterDataModule } from './master-data/master-data.module';
import { MasterDataService } from './master-data/master-data.service';
import { StaticDataService } from './static-data.service';
import { PermissionService } from '../services/permission/permission.service';
import { PdfViewService } from './pdfview.service';
import { NotifyService } from '../services/notify/notify-service';
import { UserService } from '../services/user/user.service';
import { OktaService } from './shared/okta/okta.service';
import { ConfigurationModule } from './configuration/configuration.module';
import { CustomFieldGenerationModule } from './custom-field-generation/custom-field-generation.module';
import { FormReportsModule } from './form-reports/form-reports.module';

@NgModule({
  imports: [
    PagesModule,
    ProfileModule,
    RegitrationModule,
    CommonModule,
    DashboardModule,
    AccountModule,
    UsersModule,
    SignaturesModule,
    SuppliesModule,
    InboxModule,
    ArchiveModule,
    MessageModule,
    EducationModule,
    ManageAlertModule,
    MaskedMessageModule,
    ScheduleModule,
    VisitSchedulerModule,
    VisitScheduleModule,
    LoginModule,
    OpsToolsModule,
    DocumentsModule,
    VerbalOrderModule,
    InfusionSupportModule,
    NgUploaderModule,
    FillingCenterManagerModule,
    FormManageModule,
    ExternalIntegrationModule,
    UserRegistrationModule,
    WorkListModule,
    PatientActivityHubModule,
    AddUserModule,
    EversanaModule,
    IntakeModule,
    BookedSchedulerModule,
    ManageSitesModule,
    TranslateModule.forChild(),
    MasterDataModule,
    ActivityLogsModule,
    AFSModule,
    ConfigurationModule,
    CampaignManagerModule,
    CustomFieldGenerationModule,
    FormReportsModule
  ],
  providers: [
    DocumentCenterService,
    FileValidationService,
    StructureService,
    ToolTipService,
    videoTutorialService,
    InboxService,
    ArchiveService,
    RegistrationService,
    ChatService,
    SignService,
    ScheduleService,
    MessageBroadcastService,
    MessageViewerService,
    FormsService,
    MaskedMessageService,
    WorkListService,
    WorklistIndexdbService,
    EversanaWorkListService,
    VisitSchedulerService,
    VisitScheduleService,
    MasterDataService,
    ManageSitesService,
    ManageRoutingRulesService,
    RoutingScheduleService,
    StaticDataService,
    PermissionService,
    PdfViewService,
    MessageService,
    TagService,
    NotifyService,
    UserService,
    OktaService,
    SearchService
  ],
  exports: [TranslateModule]
})
export class StructureModule {}
